[package]
name = "apalis-redis"
version = "0.7.3"
authors = ["<PERSON><PERSON><PERSON><PERSON> <mureith<PERSON><PERSON><PERSON><PERSON>@gmail.com>"]
edition.workspace = true
repository.workspace = true
readme = "../../README.md"

license = "MIT"
description = "Redis Storage for apalis: use Redis for background jobs and message queueing"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
apalis-core = { path = "../../packages/apalis-core", version = "0.7.3", default-features = false, features = [
    "sleep",
    "json",
] }
redis = { version = "0.32", default-features = false, features = [
    "script",
    "aio",
    "connection-manager",
] }
serde = "1"
log = "0.4.21"
chrono = { version = "0.4.38", default-features = false, features = [
    "clock",
    "serde",
] }
futures = "0.3.30"
tokio = { version = "1", features = ["rt", "net"], optional = true }
async-std = { version = "1.13.0", optional = true }
thiserror = "2.0.0"


[dev-dependencies]
tokio = { version = "1", features = ["macros", "rt-multi-thread"] }
email-service = { path = "../../examples/email-service" }
apalis = { path = "../../", default-features = false }
apalis-core = { path = "../apalis-core", features = ["test-utils"] }

[features]
default = ["tokio-comp"]
async-std-comp = ["async-std", "redis/async-std-comp"]
tokio-comp = ["tokio", "tokio/net", "redis/tokio-comp"]
