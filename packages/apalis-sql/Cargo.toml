[package]
name = "apalis-sql"
version = "0.7.3"
authors = ["<PERSON><PERSON><PERSON><PERSON> <mureith<PERSON><PERSON><PERSON><PERSON>@gmail.com>"]
edition.workspace = true
repository.workspace = true
readme = "../../README.md"

license = "MIT"
description = "SQL Storage for apalis. Use sqlite, postgres and mysql for background job processing"

[features]
default = ["migrate"]
postgres = ["sqlx/postgres", "sqlx/json"]
sqlite = ["sqlx/sqlite", "sqlx/json"]
mysql = ["sqlx/mysql", "sqlx/json", "sqlx/bigdecimal"]
migrate = ["sqlx/migrate", "sqlx/macros"]
async-std-comp = ["async-std", "sqlx/runtime-async-std-rustls"]
async-std-comp-native-tls = ["async-std", "sqlx/runtime-async-std-native-tls"]
tokio-comp = ["tokio", "sqlx/runtime-tokio-rustls"]
tokio-comp-native-tls = ["tokio", "sqlx/runtime-tokio-native-tls"]

[dependencies.sqlx]
version = "0.8.1"
default-features = false
features = ["chrono"]

[dependencies]
serde = { version = "1", features = ["derive"] }
serde_json = "1"
apalis-core = { path = "../../packages/apalis-core", version = "0.7.3", default-features = false, features = [
    "sleep",
    "json",
] }
log = "0.4.21"
futures = "0.3.30"
async-stream = "0.3.5"
tokio = { version = "1", features = ["rt", "net"], optional = true }
futures-lite = "2.3.0"
async-std = { version = "1.13.0", optional = true }
chrono = { version = "0.4", features = ["serde"] }
thiserror = "2.0.0"


[dev-dependencies]
tokio = { version = "1", features = ["macros", "rt-multi-thread"] }
email-service = { path = "../../examples/email-service" }
apalis = { path = "../../", default-features = false }
once_cell = "1.19.0"
apalis-sql = { path = ".", features = ["tokio-comp"] }
apalis-core = { path = "../apalis-core", features = ["test-utils"] }

[package.metadata.docs.rs]
# defines the configuration attribute `docsrs`
rustdoc-args = ["--cfg", "docsrs"]
all-features = true
