[package]
name = "apalis-cron"
version = "0.7.3"
edition.workspace = true
repository.workspace = true
authors = ["<PERSON><PERSON><PERSON><PERSON> <mureithinju<PERSON><EMAIL>>"]
license = "MIT"
description = "A simple yet extensible library for cron-like job scheduling for rust."

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
apalis-core = { path = "../../packages/apalis-core", version = "0.7.3", default-features = false, features = [
    "sleep",
] }
cron = "0.15.0"
futures = "0.3.30"
tower = { version = "0.5" }
chrono = { version = "0.4.38", default-features = false, features = [
    "clock",
    "serde",
] }
async-stream = "0.3.5"
async-std = { version = "1.13.0", optional = true }

[dev-dependencies]
tokio = { version = "1", features = ["macros"] }
apalis-core = { path = "../../packages/apalis-core" }
apalis = { path = "../../", default-features = false, features = ["retry"] }
serde = { version = "1.0", features = ["derive"] }


[package.metadata.docs.rs]
# defines the configuration attribute `docsrs`
rustdoc-args = ["--cfg", "docsrs"]
all-features = true
