[package]
name = "apalis-core"
version = "0.7.3"
authors = ["<PERSON><PERSON><PERSON><PERSON> <mureith<PERSON><PERSON><PERSON><PERSON>@gmail.com>"]
edition.workspace = true
repository.workspace = true
license = "MIT"
description = "Core for apalis: simple, extensible multithreaded background processing for Rust"
categories = ["concurrency"]
readme = "../../README.md"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html


[dependencies]
serde = { version = "1.0", features = ["derive"] }
futures = { version = "0.3.30", features = ["async-await"] }
tower = { version = "0.5", features = ["util"], default-features = false }
pin-project-lite = "0.2.14"
thiserror = "2.0.0"
ulid = { version = "1.1.2", default-features = false, features = ["std"] }
futures-timer = { version = "3.0.3", optional = true }
# Needed for the codec
serde_json = { version = "1", optional = true }

[dependencies.document-features]
version = "0.2"
optional = true


[features]
default = []
docsrs = ["document-features"]
sleep = ["futures-timer"]
json = ["serde_json"]
test-utils = []

[package.metadata.docs.rs]
# defines the configuration attribute `docsrs`
rustdoc-args = ["--cfg", "docsrs"]
all-features = true


[dev-dependencies]
tokio = { version = "1.37.0", features = ["macros", "rt", "sync"] }
tokio-stream = "0.1.15"
