on:
  pull_request:
    paths:
      - "packages/apalis-sql/src/lib.rs"
      - "packages/apalis-sql/src/postgres.rs"
      - "packages/apalis-sql/src/migrations/postgres/**"
      - "packages/apalis-sql/src/Cargo.toml"
      - ".github/workflows/postgres.yaml"
      - "packages/apalis-core/**"

name: Postgres CI
permissions:
  contents: read

jobs:
  test-sqlite:
    name: Test Suite Postgres
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:17
        env:
          POSTGRES_PASSWORD: postgres
        ports:
          - 5432:5432
    env:
      DATABASE_URL: postgres://postgres:postgres@localhost/postgres
    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5
      - uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: stable
          override: true
      - run: cargo test --no-default-features --features postgres,migrate,tokio-comp -- --test-threads=1
        working-directory: packages/apalis-sql
