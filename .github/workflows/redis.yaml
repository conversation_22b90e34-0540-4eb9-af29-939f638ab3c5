on:
  pull_request:
    paths:
      - "packages/apalis-redis/**"
      - ".github/workflows/redis.yaml"
      - "packages/apalis-core/**"


name: Redis CI
permissions:
  contents: read

jobs:
  test-redis:
    name: Test Suite Redis
    runs-on: ubuntu-latest
    services:
      redis:
        image: redis
        ports:
          - 6379:6379
    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5
      - uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: stable
          override: true
      - run: cargo test -- --test-threads=1
        working-directory: packages/apalis-redis
        env:
          REDIS_URL: redis://127.0.0.1/
