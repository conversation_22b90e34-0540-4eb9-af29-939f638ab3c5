on:
  pull_request:
    paths:
      - "packages/apalis-sql/src/lib.rs"
      - "packages/apalis-sql/src/sqlite.rs"
      - "packages/apalis-sql/src/migrations/sqlite/**"
      - "packages/apalis-sql/src/Cargo.toml"
      - ".github/workflows/sqlite.yaml"
      - "packages/apalis-core/**"


name: Sqlite CI
permissions:
  contents: read

jobs:
  test-sqlite:
    name: Test Suite Sqlite
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5
      - uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: stable
          override: true
      - run: cargo test --no-default-features --features sqlite,migrate,tokio-comp -- --test-threads=1
        working-directory: packages/apalis-sql
