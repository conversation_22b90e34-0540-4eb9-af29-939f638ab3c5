[package]
name = "async-std-runtime"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
anyhow = "1"
apalis = { path = "../../", default-features = false, features = [
    "tracing",
    "retry",
] }
apalis-cron = { path = "../../packages/apalis-cron" }
apalis-core = { path = "../../packages/apalis-core", default-features = false }
async-std = { version = "1.13.0", features = ["attributes"] }
serde = "1"
tracing-subscriber = "0.3.11"
chrono = { version = "0.4", default-features = false, features = ["clock"] }
pin-project-lite = "0.2.9"
ctrlc = "3.2.5"
async-channel = "2"

[dependencies.tracing]
default-features = false
version = "0.1"
