[package]
name = "sqlite-example"
version = "0.1.0"
authors = ["<PERSON><PERSON><PERSON><PERSON> <mureithinjugun<PERSON>@gmail.com>"]
edition = "2018"
license = "MIT OR Apache-2.0"

[dependencies]
anyhow = "1"
tokio = { version = "1", features = ["full"] }
apalis = { path = "../../", features = ["limit", "tracing"] }
apalis-sql = { path = "../../packages/apalis-sql", features = [
    "sqlite",
    "tokio-comp",
] }
serde = { version = "1", features = ["derive"] }
tracing-subscriber = "0.3.11"
chrono = { version = "0.4", default-features = false, features = ["clock"] }
email-service = { path = "../email-service" }


[dependencies.tracing]
default-features = false
version = "0.1"

[dependencies.sqlx]
version = "0.8.1"
default-features = false
features = ["sqlite"]
