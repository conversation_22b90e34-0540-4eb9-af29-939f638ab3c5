[package]
name = "postgres-example"
version = "0.1.0"
authors = ["<PERSON><PERSON><PERSON><PERSON> <mureith<PERSON><PERSON><PERSON><PERSON>@gmail.com>"]
edition = "2018"
license = "MIT OR Apache-2.0"

[dependencies]
anyhow = "1"
apalis = { path = "../../", features = ["retry"] }
apalis-sql = { path = "../../packages/apalis-sql", features = [
    "postgres",
    "tokio-comp",
] }
serde = "1"
tracing-subscriber = "0.3.11"
chrono = { version = "0.4", default-features = false, features = ["clock"] }
tokio = { version = "1", features = ["full"] }
email-service = { path = "../email-service" }

[dependencies.tracing]
default-features = false
version = "0.1"
