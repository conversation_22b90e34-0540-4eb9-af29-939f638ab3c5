[package]
name = "redis-example"
version = "0.1.0"
authors = ["<PERSON><PERSON><PERSON><PERSON> <mureithin<PERSON><PERSON><PERSON>@gmail.com>"]
edition = "2018"
license = "MIT OR Apache-2.0"

[dependencies]
anyhow = "1"
tokio = { version = "1", features = ["full"] }
apalis = { path = "../../", features = ["timeout", "limit"] }
apalis-redis = { path = "../../packages/apalis-redis" }
serde = "1"
env_logger = "0.10"
tracing-subscriber = "0.3.11"
chrono = { version = "0.4", default-features = false, features = ["clock"] }
email-service = { path = "../email-service" }


[dependencies.tracing]
default-features = false
version = "0.1"
