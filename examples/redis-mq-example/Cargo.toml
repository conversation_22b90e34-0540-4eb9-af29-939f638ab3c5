[package]
name = "redis-mq-example"
version = "0.1.0"
edition = "2021"

[dependencies]
apalis = { path = "../.." }
apalis-redis = { path = "../../packages/apalis-redis" }
apalis-core = { path = "../../packages/apalis-core", features = ["json"] }
rsmq_async = "13.0.0"
anyhow = "1"
tokio = { version = "1", features = ["full"] }
serde = "1"
env_logger = "0.10"
tracing-subscriber = "0.3.11"
chrono = { version = "0.4", default-features = false, features = ["clock"] }
email-service = { path = "../email-service" }
rmp-serde = "1.3"
tower = "0.4"
futures = "0.3"


[dependencies.tracing]
default-features = false
version = "0.1"
