[package]
name = "basics"
version = "0.1.0"
authors = ["<PERSON><PERSON><PERSON><PERSON> <mureithin<PERSON>gun<PERSON>@gmail.com>"]
edition = "2021"
license = "MIT OR Apache-2.0"

[dependencies]
thiserror = "2.0.0"
tokio = { version = "1", features = ["full"] }
apalis = { path = "../../", features = ["limit", "catch-panic"] }
apalis-sql = { path = "../../packages/apalis-sql", features = [
    "sqlite",
    "tokio-comp",
] }
serde = "1"
tracing-subscriber = "0.3.11"
email-service = { path = "../email-service" }
futures = "0.3"
tower = "0.4"


[dependencies.tracing]
default-features = false
version = "0.1"
