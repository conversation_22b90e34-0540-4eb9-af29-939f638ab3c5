[package]
name = "axum-example"
version = "0.1.0"
edition = "2018"
publish = false

[dependencies]
anyhow = "1"
axum = "0.5.6"
tokio = { version = "1.0", features = ["full"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
serde = { version = "1.0", features = ["derive"] }
apalis = { path = "../../" }
apalis-redis = { path = "../../packages/apalis-redis" }
futures = "0.3"
email-service = { path = "../email-service" }
