[package]
name = "redis-deadpool"
version = "0.1.0"
edition = "2021"

[dependencies]
deadpool-redis = "0.22.0"
anyhow = "1"
tokio = { version = "1", features = ["full"] }
apalis = { path = "../../", features = ["timeout"] }
apalis-redis = { path = "../../packages/apalis-redis" }
serde = "1"
env_logger = "0.10"
tracing-subscriber = "0.3.11"
chrono = { version = "0.4", default-features = false, features = ["clock"] }
email-service = { path = "../email-service" }
rmp-serde = "1.3"


[dependencies.tracing]
default-features = false
version = "0.1"
