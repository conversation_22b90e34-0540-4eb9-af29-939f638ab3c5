[package]
name = "tracing-example"
version = "0.1.0"
authors = ["<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"]
edition = "2018"
license = "MIT OR Apache-2.0"

[dependencies]
anyhow = "1"
apalis = { path = "../../" }
apalis-redis = { path = "../../packages/apalis-redis" }
serde = "1"
tokio = { version = "1", features = ["full"] }
env_logger = "0.10"
tracing-subscriber = { version = "0.3.11", features = ["env-filter", "json"] }
chrono = { version = "0.4", default-features = false, features = ["clock"] }
email-service = { path = "../email-service" }
futures = "0.3"


[dependencies.tracing]
default-features = false
version = "0.1"
