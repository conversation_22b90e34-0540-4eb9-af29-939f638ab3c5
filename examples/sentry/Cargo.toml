[package]
name = "sentry-example"
version = "0.1.0"
authors = ["<PERSON><PERSON><PERSON><PERSON> <mureith<PERSON><PERSON><PERSON><PERSON>@gmail.com>"]
edition = "2018"
license = "MIT OR Apache-2.0"

[dependencies]
anyhow = "1"
apalis = { path = "../../", features = ["sentry"] }
apalis-redis = { path = "../../packages/apalis-redis" }
serde = "1"
env_logger = "0.10"
tracing-subscriber = { version = "0.3.11", features = ["env-filter"] }
sentry = "0.37.0"
sentry-tower = "0.37.0"
sentry-tracing = "0.37.0"
chrono = { version = "0.4", default-features = false, features = ["clock"] }
tokio = { version = "1", features = ["macros"] }
email-service = { path = "../email-service" }


[dependencies.tracing]
default-features = false
version = "0.1"
