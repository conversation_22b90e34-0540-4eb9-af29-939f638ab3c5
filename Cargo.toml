[workspace.package]
edition = "2021"
repository = "https://github.com/geofmureithi/apalis"

[package]
name = "apalis"
version = "0.7.3"
authors = ["<PERSON> <<EMAIL>>"]
description = "Simple, extensible multithreaded background job processing for Rust"
edition.workspace = true
repository.workspace = true
documentation = "https://docs.rs/apalis"
readme = "README.md"
license = "MIT OR Apache-2.0"
keywords = ["job", "task", "scheduler", "worker", "cron"]
categories = ["database"]

[lib]
bench = false

[features]
default = ["tracing"]


## Support Tracing 👀
tracing = ["dep:tracing", "dep:tracing-futures"]

## Support for Sentry exception and performance monitoring
sentry = ["sentry-core", "ulid?/uuid", "uuid"]
## Support Prometheus metrics
prometheus = ["metrics", "metrics-exporter-prometheus"]
## Support direct retrying jobs
retry = ["tower/retry"]
## Support timeouts on jobs
timeout = ["tower/timeout"]
## 💪 Limit the amount of jobs
limit = ["tower/limit"]
## Support filtering jobs based on a predicate
filter = ["tower/filter"]
## Captures panics in executions and convert them to errors
catch-panic = []

layers = [
  "sentry",
  "prometheus",
  "tracing",
  "retry",
  "timeout",
  "limit",
  "filter",
  "catch-panic",
]

docsrs = ["document-features"]

[dependencies.apalis-core]
version = "0.7.3"
default-features = false
path = "./packages/apalis-core"

[dependencies.document-features]
version = "0.2"
optional = true


[package.metadata.docs.rs]
# defines the configuration attribute `docsrs`
rustdoc-args = ["--cfg", "docsrs"]
all-features = true


[dev-dependencies]
criterion = { version = "0.7.0", features = ["async_tokio", "html_reports"] }
pprof = { version = "0.15", features = ["flamegraph"] }
paste = "1.0.14"
serde = "1"
tokio = { version = "1", features = ["macros", "rt-multi-thread"] }
apalis = { path = ".", features = ["limit"] }
apalis-redis = { path = "./packages/apalis-redis" }
apalis-sql = { path = "./packages/apalis-sql", features = [
  "postgres",
  "mysql",
  "sqlite",
] }
redis = { version = "0.32", default-features = false, features = [
  "tokio-comp",
  "script",
  "aio",
  "connection-manager",
] }

[dev-dependencies.sqlx]
version = "0.8.1"
default-features = false
features = ["chrono", "mysql", "sqlite", "postgres", "runtime-tokio"]

[workspace]
members = [
  "packages/apalis-core",
  "packages/apalis-redis",
  "packages/apalis-sql",
  "packages/apalis-cron",
  # Examples
  "examples/email-service",
  "examples/redis",
  "examples/actix-web",
  "examples/sqlite",
  "examples/sentry",
  "examples/mysql",
  "examples/postgres",
  "examples/axum",
  "examples/prometheus",
  "examples/tracing",
  "examples/async-std-runtime",
  "examples/basics",
  "examples/redis-with-msg-pack",
  "examples/redis-deadpool",
  "examples/redis-mq-example",
  "examples/cron",
  "examples/catch-panic",
  "examples/graceful-shutdown",
  "examples/unmonitored-worker",
  "examples/fn-args",
  "examples/persisted-cron",
  "examples/rest-api", 
  "examples/stepped-tasks",
  "examples/retries",
]


[dependencies]
tower = { version = "0.5", features = ["util"], default-features = false }
tracing-futures = { version = "0.2.5", optional = true, default-features = false }
sentry-core = { version = "0.42.0", optional = true, default-features = false }
metrics = { version = "0.24.0", optional = true, default-features = false }
metrics-exporter-prometheus = { version = "0.17", optional = true, default-features = false }
thiserror = "2.0.0"
futures = "0.3.30"
pin-project-lite = "0.2.14"
# Needed only for sentry reporting
uuid = { version = "1.8", optional = true }
ulid = { version = "1", optional = true }
serde = { version = "1.0", features = ["derive"] }

[dependencies.tracing]
default-features = false
version = "0.1.40"
optional = true
